/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.logo {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Navigation Tabs */
.nav-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 30px;
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.tab-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    padding: 15px 10px;
    border-radius: 10px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.tab-icon {
    font-size: 1.5rem;
}

.tab-text {
    font-size: 0.9rem;
}

/* Main Content */
.main-content {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 20px;
}

.tool-section {
    display: none;
}

.tool-section.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tool-header {
    text-align: center;
    margin-bottom: 30px;
}

.tool-header h2 {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
}

.tool-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Form Elements */
.category-selector {
    margin-bottom: 25px;
}

.category-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

select, textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

select:focus, textarea:focus {
    outline: none;
    border-color: #667eea;
}

textarea {
    min-height: 100px;
    resize: vertical;
}

.custom-options {
    margin-top: 20px;
}

.custom-options small {
    color: #666;
    margin-top: 5px;
    display: block;
}

/* Buttons */
.generate-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 30px;
    border-radius: 12px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 25px 0;
    position: relative;
    overflow: hidden;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.generate-btn:active {
    transform: translateY(0);
}

.generate-btn.loading .btn-text {
    opacity: 0;
}

.generate-btn.loading .btn-spinner {
    display: inline-block !important;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.btn-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.5rem;
}

/* Sub-tabs for Dice & Coin */
.dice-coin-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
}

.sub-tab-btn {
    flex: 1;
    background: #f8f9fa;
    border: 2px solid #e1e5e9;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sub-tab-btn:hover {
    background: #e9ecef;
}

.sub-tab-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.sub-tool {
    display: none;
}

.sub-tool.active {
    display: block;
}

/* Result Displays */
.result-display {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    margin: 25px 0;
    min-height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #e1e5e9;
    transition: all 0.3s ease;
}

.result-display.has-result {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    animation: resultAppear 0.6s ease;
}

@keyframes resultAppear {
    from { 
        opacity: 0; 
        transform: scale(0.8) rotateY(90deg); 
    }
    to { 
        opacity: 1; 
        transform: scale(1) rotateY(0deg); 
    }
}

.result-text {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.result-meta {
    font-size: 1rem;
    opacity: 0.8;
}

/* Quote specific styling */
.quote-result.has-result {
    text-align: left;
    padding: 40px;
}

.quote-text {
    font-size: 1.3rem;
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.8;
}

.quote-author {
    font-size: 1.1rem;
    font-weight: 600;
    text-align: right;
}

.quote-author::before {
    content: "— ";
}

/* Dice Display */
.dice-display {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 30px 0;
}

.die {
    width: 80px;
    height: 80px;
    background: white;
    border: 3px solid #667eea;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    animation: diceRoll 0.8s ease;
}

@keyframes diceRoll {
    0%, 100% { transform: rotateX(0deg) rotateY(0deg); }
    25% { transform: rotateX(90deg) rotateY(0deg); }
    50% { transform: rotateX(180deg) rotateY(90deg); }
    75% { transform: rotateX(270deg) rotateY(180deg); }
}

/* Coin Display */
.coin-display {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.coin {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    border: 4px solid #f59e0b;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    font-weight: bold;
    color: #92400e;
    animation: coinFlip 1s ease;
}

@keyframes coinFlip {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
}

/* Action Buttons */
.quote-actions, .activity-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.action-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.action-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

/* Footer */
.footer {
    text-align: center;
    color: white;
    padding: 20px 0;
    opacity: 0.8;
}

.footer-links {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 0.7;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
    color: #667eea;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 25px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .logo {
        font-size: 2rem;
    }
    
    .tagline {
        font-size: 1rem;
    }
    
    .nav-tabs {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 12px;
    }
    
    .tab-btn {
        padding: 12px 8px;
    }
    
    .tab-icon {
        font-size: 1.2rem;
    }
    
    .tab-text {
        font-size: 0.8rem;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .tool-header h2 {
        font-size: 1.6rem;
    }
    
    .tool-header p {
        font-size: 1rem;
    }
    
    .generate-btn {
        font-size: 1.1rem;
        padding: 16px 25px;
    }
    
    .die {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .coin {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }
    
    .quote-actions, .activity-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .action-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .nav-tabs {
        grid-template-columns: 1fr;
    }
    
    .dice-coin-tabs {
        flex-direction: column;
    }
    
    .dice-display {
        gap: 15px;
    }
    
    .die {
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
    }
    
    .coin {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }
}
